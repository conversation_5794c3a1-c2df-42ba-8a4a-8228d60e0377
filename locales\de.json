{"progress": {"attachHarness": "Renn<PERSON><PERSON><PERSON>", "removeHarness": "<PERSON><PERSON><PERSON><PERSON><PERSON> ab<PERSON>en"}, "notify": {"notInCar": "Du bist nicht in einem Auto", "seatbeltOn": "Sicherheitsgurt An", "seatbeltOff": "Sicherheitsgurt Aus"}, "error": {"seatbelton": "Nimm zuerst deinen Sicherheitsgurt ab", "harnesson": "<PERSON>mm zu<PERSON>t deinen Gurt ab"}, "toggleCommand": "Sicherheitsgurt umschalten"}